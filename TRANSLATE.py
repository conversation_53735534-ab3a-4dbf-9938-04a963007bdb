import pyaudio
import numpy as np
from googletrans import Translator
import edge_tts
import pygame
import asyncio
from gtts import gTTS
import threading
from concurrent.futures import ThreadPoolExecutor
import time
import io
from collections import deque
import torch
from queue import Queue, PriorityQueue
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import customtkinter as ctk
from datetime import datetime
import json
import os
import tempfile
import requests
from pydub import AudioSegment # New import for audio processing
from pydub.playback import play # Not directly used for PyAudio, but useful for debugging
from g4f.client import Client # New import for g4f GPT provider
import g4f
import websocket
import ssl
import base64

# Import RealtimeSTT for real-time speech recognition
from RealtimeSTT import AudioToTextRecorder
print("✅ Using RealtimeSTT for real-time speech recognition")

# Try to import Azure Speech SDK for enhanced real-time capabilities
try:
    import azure.cognitiveservices.speech as speechsdk
    AZURE_SPEECH_AVAILABLE = True
    print("✅ Azure Speech SDK available for enhanced real-time translation")
except ImportError:
    AZURE_SPEECH_AVAILABLE = False
    print("⚠️ Azure Speech SDK not available. Install with: pip install azure-cognitiveservices-speech")

# Try to import OpenAI for Realtime API
try:
    import openai
    OPENAI_AVAILABLE = True
    print("✅ OpenAI SDK available for Realtime API")
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI SDK not available. Install with: pip install openai")

# Set appearance mode and color theme for CustomTkinter
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class WhisperRealTimeTranslator:
    def __init__(self, gui_callback=None):
        self.gui_callback = gui_callback

        # Enhanced real-time configuration
        self.realtime_mode = "enhanced"  # "basic", "enhanced", "azure", "openai"
        self.streaming_tts_enabled = True
        self.azure_speech_key = None
        self.azure_speech_region = None
        self.openai_api_key = None

        # Language configuration with hybrid TTS support
        # 'tts_engine': 'edge' for Edge TTS, 'gtts' for Google TTS, 'azure' for Azure TTS
        self.languages = {
            'Japanese': {'code': 'ja', 'voice': 'ja-JP-NanamiNeural', 'tts_engine': 'edge'},
            'Spanish': {'code': 'es', 'voice': 'es-ES-ElviraNeural', 'tts_engine': 'edge'},
            'French': {'code': 'fr', 'voice': 'fr-FR-DeniseNeural', 'tts_engine': 'edge'},
            'German': {'code': 'de', 'voice': 'de-DE-KatjaNeural', 'tts_engine': 'edge'},
            'Italian': {'code': 'it', 'voice': 'it-IT-ElsaNeural', 'tts_engine': 'edge'},
            'Portuguese': {'code': 'pt', 'voice': 'pt-BR-FranciscaNeural', 'tts_engine': 'edge'},
            'Russian': {'code': 'ru', 'voice': 'ru-RU-SvetlanaNeural', 'tts_engine': 'edge'},
            'Chinese (Simplified)': {'code': 'zh-cn', 'voice': 'zh-CN-XiaoxiaoNeural', 'tts_engine': 'edge'},
            'Chinese (Traditional)': {'code': 'zh-tw', 'voice': 'zh-TW-HsiaoyuNeural', 'tts_engine': 'edge'},
            'Korean': {'code': 'ko', 'voice': 'ko-KR-SunHiNeural', 'tts_engine': 'edge'},
            'Hindi': {'code': 'hi', 'voice': 'hi-IN-SwaraNeural', 'tts_engine': 'edge'},
            'Arabic': {'code': 'ar', 'voice': 'ar-SA-ZariyahNeural', 'tts_engine': 'edge'},
            'Dutch': {'code': 'nl', 'voice': 'nl-NL-ColetteNeural', 'tts_engine': 'edge'},
            'Swedish': {'code': 'sv', 'voice': 'sv-SE-SofieNeural', 'tts_engine': 'edge'},
            'Norwegian': {'code': 'no', 'voice': 'nb-NO-PernilleNeural', 'tts_engine': 'edge'},
            'Danish': {'code': 'da', 'voice': 'da-DK-ChristelNeural', 'tts_engine': 'edge'},
            'Finnish': {'code': 'fi', 'voice': 'fi-FI-SelmaNeural', 'tts_engine': 'edge'},
            'Polish': {'code': 'pl', 'voice': 'pl', 'tts_engine': 'gtts'},  # Use Google TTS for Polish
            'Czech': {'code': 'cs', 'voice': 'cs', 'tts_engine': 'gtts'},  # Use Google TTS for Czech
            'Hungarian': {'code': 'hu', 'voice': 'hu-HU-NoemiNeural', 'tts_engine': 'edge'},
            'Romanian': {'code': 'ro', 'voice': 'ro-RO-AlinaNeural', 'tts_engine': 'edge'},
            'Greek': {'code': 'el', 'voice': 'el-GR-AthinaNeural', 'tts_engine': 'edge'},
            'Turkish': {'code': 'tr', 'voice': 'tr-TR-EmelNeural', 'tts_engine': 'edge'},
            'Hebrew': {'code': 'he', 'voice': 'he-IL-HilaNeural', 'tts_engine': 'edge'},
            'Thai': {'code': 'th', 'voice': 'th-TH-AcharaNeural', 'tts_engine': 'edge'},
            'Vietnamese': {'code': 'vi', 'voice': 'vi-VN-HoaiMyNeural', 'tts_engine': 'edge'},
            'Indonesian': {'code': 'id', 'voice': 'id-ID-GadisNeural', 'tts_engine': 'edge'},
            'Malay': {'code': 'ms', 'voice': 'ms-MY-YasminNeural', 'tts_engine': 'edge'},
            'Filipino': {'code': 'tl', 'voice': 'fil-PH-AngeloNeural', 'tts_engine': 'edge'},
            'Ukrainian': {'code': 'uk', 'voice': 'uk-UA-PolinaNeural', 'tts_engine': 'edge'},
            'Bulgarian': {'code': 'bg', 'voice': 'bg-BG-KalinaNeural', 'tts_engine': 'edge'},
            'Croatian': {'code': 'hr', 'voice': 'hr-HR-GabrijelaNeural', 'tts_engine': 'edge'},
            'Serbian': {'code': 'sr', 'voice': 'sr-RS-SophieNeural', 'tts_engine': 'edge'},
            'Slovak': {'code': 'sk', 'voice': 'sk', 'tts_engine': 'gtts'},  # Use Google TTS for Slovak
            'Slovenian': {'code': 'sl', 'voice': 'sl-SI-PetraNeural', 'tts_engine': 'edge'},
            'Lithuanian': {'code': 'lt', 'voice': 'lt', 'tts_engine': 'gtts'},  # Use Google TTS for Lithuanian
            'Latvian': {'code': 'lv', 'voice': 'lv', 'tts_engine': 'gtts'},  # Use Google TTS for Latvian
            'Estonian': {'code': 'et', 'voice': 'et', 'tts_engine': 'gtts'},  # Use Google TTS for Estonian
            'English': {'code': 'en', 'voice': 'en-US-AriaNeural', 'tts_engine': 'edge'},
        }
        
        # Default target language
        self.target_language = 'English' # Changed default to English
        self.translation_service = 'Google Translate'  # Default service
        self.ollama_messages = []
        self.gpt_client = None # New: g4f client for GPT
        
        # Initialize RealtimeSTT recorder for real-time transcription with GPU support
        self.log_to_gui("Initializing RealtimeSTT with GPU acceleration...")

        # Check if CUDA is available
        device = "cuda" if torch.cuda.is_available() else "cpu"
        compute_type = "float16" if torch.cuda.is_available() else "float32"

        # Define callbacks for RealtimeSTT
        def on_recording_start():
            """Called when RealtimeSTT starts recording"""
            self.log_to_gui("🎤 Started speaking...")

        def on_recording_stop():
            """Called when RealtimeSTT stops recording"""
            self.log_to_gui("⏸️ Pause detected, processing speech...")

        # Define real-time transcription callbacks
        def on_realtime_transcription_update(text):
            """Called for every real-time transcription update (partial text)"""
            if text and text.strip():
                self.log_to_gui(f"🔄 Live: {text}")
                # Process individual words for immediate translation and TTS
                self.process_realtime_words(text)

        def on_realtime_transcription_stabilized(text):
            """Called when real-time transcription is stabilized (more accurate)"""
            if text and text.strip():
                self.log_to_gui(f"✅ Stable: {text}")

        self.realtime_recorder = AudioToTextRecorder(
            model="medium",  # Using medium model for better accuracy
            language="en",
            device=device,  # Use GPU if available
            compute_type=compute_type,  # Use float16 for GPU, float32 for CPU
            use_microphone=True,  # Let RealtimeSTT handle microphone
            spinner=False,  # Disable spinner for GUI
            level=30,  # Reduce logging
            ensure_sentence_starting_uppercase=True,
            ensure_sentence_ends_with_period=True,
            post_speech_silence_duration=0.8,  # Match our pause detection
            min_length_of_recording=0.5,  # Minimum recording length
            min_gap_between_recordings=0.3,  # Gap between recordings
            silero_sensitivity=0.6,  # Voice activity detection sensitivity
            webrtc_sensitivity=3,  # WebRTC VAD sensitivity
            silero_use_onnx=True,  # Use ONNX for better performance
            beam_size=1,  # Faster beam search for real-time
            batch_size=16 if torch.cuda.is_available() else 8,  # Larger batch size for GPU
            on_recording_start=on_recording_start,  # Callback for recording start
            on_recording_stop=on_recording_stop,  # Callback for recording stop
            # Enable real-time transcription
            enable_realtime_transcription=True,
            use_main_model_for_realtime=False,  # Use separate faster model for real-time
            realtime_model_type="base",  # Use base model for better real-time accuracy
            realtime_processing_pause=0.1,  # Process every 100ms for maximum responsiveness
            on_realtime_transcription_update=on_realtime_transcription_update,
            on_realtime_transcription_stabilized=on_realtime_transcription_stabilized,
            realtime_batch_size=8 if torch.cuda.is_available() else 4,  # Smaller batch for speed
            beam_size_realtime=1  # Fastest beam search for real-time
        )

        if torch.cuda.is_available():
            self.log_to_gui(f"✅ RealtimeSTT initialized with GPU acceleration! (Device: {device}, Compute: {compute_type})")
        else:
            self.log_to_gui("✅ RealtimeSTT initialized! (CPU mode - install CUDA for GPU acceleration)")

        # Flag to indicate if we should use RealtimeSTT or PyAudio
        self.use_realtime_stt = True
        
        # Audio setup - optimized for real-time processing
        self.audio_format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.chunk_size = 512  # Smaller chunks for more responsive processing
        self.buffer_duration = 0.1  # 100ms buffer for real-time feel
        
        self.audio_instance = pyaudio.PyAudio() # Create PyAudio instance once
        self.stream = None
        self.output_device_index = None # New: Store selected output device index
        
        self.setup_audio() # This is now setup_audio_input
        self.setup_audio_output() # Initialize pygame mixer here, early
        
        # Translation and TTS
        self.translator = Translator()
        
        # Enhanced TTS Queue Management with Priority
        self.tts_queue = PriorityQueue()  # Priority queue for better real-time handling
        self.is_tts_playing = False
        self.tts_lock = threading.Lock()
        self.tts_cache = {}  # Cache for TTS audio files
        self.max_tts_cache_size = 50  # Limit cache size
        self.tts_generation_queue = PriorityQueue()  # Priority queue for pre-generated TTS items
        self.streaming_tts_queue = Queue()  # Queue for streaming TTS chunks

        # Azure Speech SDK components
        self.azure_speech_config = None
        self.azure_translation_recognizer = None
        self.azure_speech_synthesizer = None
        
        # Threading and caching
        self.is_running = True  # Always running now
        self.translation_cache = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.is_processing = False
        
        # Voice mode variables - optimized for real-time
        self.ambient_noise_level = 0
        self.voice_threshold = 0
        self.silence_threshold = 0
        self.speech_buffer = b""
        self.is_speaking = False
        self.last_voice_time = 0
        self.silence_duration = 0
        self.min_speech_duration = 0.3  # Reduced minimum speech duration
        self.pause_threshold = 0.8  # Reduced pause threshold for faster response
        self.max_speech_duration = 30.0  # Reduced max duration for faster processing

        # Real-time processing variables
        self.audio_buffer = deque(maxlen=int(self.rate * 2))  # 2-second rolling buffer
        self.processing_interval = 0.5  # Process every 500ms for real-time feel

        # Performance monitoring
        self.performance_stats = {
            'transcription_times': deque(maxlen=10),
            'translation_times': deque(maxlen=10),
            'tts_times': deque(maxlen=10),
            'total_processing_times': deque(maxlen=10)
        }
        
        # Mode control - starts in voice mode
        self.mode = "voice"  # "voice" or "typing"
        self.voice_mode_active = False
        self.typing_mode_active = False
        
        # TTS worker control
        self.tts_worker_running = True
        
        # Start TTS worker threads
        self.tts_worker_thread = threading.Thread(target=self.tts_worker, daemon=True)
        self.tts_worker_thread.start()

        # Start TTS generation worker thread (pre-generates audio)
        self.tts_generation_worker_thread = threading.Thread(target=self.tts_generation_worker, daemon=True)
        self.tts_generation_worker_thread.start()
        
        # Initialize enhanced real-time services
        self.initialize_realtime_services()

        # Auto-start voice mode
        self.switch_to_voice_mode()

        self.log_to_gui("Ready! Switch between Voice and Typing modes anytime...")
    
    def set_target_language(self, language_name):
        """Set the target language for translation"""
        if language_name in self.languages:
            self.target_language = language_name
            self.log_to_gui(f"🌍 Target language set to: {language_name}")
            self.translation_cache.clear()
            if self.translation_service == 'Ollama':
                self.initialize_ollama()
            elif self.translation_service == 'GPT':
                self.initialize_gpt()
        else:
            self.log_to_gui(f"❌ Language '{language_name}' not supported")
    
    def set_output_device(self, device_name):
        """Set the audio output device for TTS playback."""
        if device_name == "None (System Default)":
            self.output_device_index = None
            self.log_to_gui("🔊 Output device set to: System Default")
        else:
            devices = self.get_output_devices()
            for index, name in devices:
                if name == device_name:
                    self.output_device_index = index
                    self.log_to_gui(f"🔊 Output device set to: {name}")
                    break
            else:
                self.log_to_gui(f"❌ Output device '{device_name}' not found.")
        self.setup_audio_output() # Re-initialize mixer with new device
    
    def get_output_devices(self):
        """Get a list of available audio output devices."""
        info = self.audio_instance.get_host_api_info_by_index(0)
        num_devices = info.get('deviceCount')
        
        output_devices = []
        for i in range(0, num_devices):
            device = self.audio_instance.get_device_info_by_host_api_device_index(0, i)
            if device.get('maxOutputChannels') > 0:
                output_devices.append((i, device.get('name')))
        return output_devices
    
    def get_current_language_info(self):
        """Get current target language information"""
        return self.languages.get(self.target_language, self.languages['Japanese'])
    
    def log_to_gui(self, message):
        """Send log message to GUI if callback is available"""
        if self.gui_callback:
            self.gui_callback(message)
        else:
            print(message)

    def set_translation_service(self, service_name):
        """Set the translation service"""
        self.translation_service = service_name
        self.log_to_gui(f"🔧 Translation service set to: {service_name}")
        self.translation_cache.clear()
        if service_name == 'Ollama':
            self.initialize_ollama()
        elif service_name == 'GPT':
            self.initialize_gpt()
        elif service_name == 'Azure Speech':
            self.initialize_azure_speech()

    def initialize_realtime_services(self):
        """Initialize enhanced real-time services"""
        self.log_to_gui("🚀 Initializing enhanced real-time services...")

        # Check for Azure Speech SDK
        if AZURE_SPEECH_AVAILABLE and self.azure_speech_key:
            self.initialize_azure_speech()

        # Check for OpenAI Realtime API
        if OPENAI_AVAILABLE and self.openai_api_key:
            self.initialize_openai_realtime()

        self.log_to_gui("✅ Real-time services initialized")

    def set_azure_credentials(self, speech_key, region):
        """Set Azure Speech Service credentials"""
        self.azure_speech_key = speech_key
        self.azure_speech_region = region
        if AZURE_SPEECH_AVAILABLE:
            self.initialize_azure_speech()
            self.log_to_gui("✅ Azure Speech credentials configured")
        else:
            self.log_to_gui("❌ Azure Speech SDK not available")

    def set_openai_key(self, api_key):
        """Set OpenAI API key for Realtime API"""
        self.openai_api_key = api_key
        if OPENAI_AVAILABLE:
            self.initialize_openai_realtime()
            self.log_to_gui("✅ OpenAI API key configured")
        else:
            self.log_to_gui("❌ OpenAI SDK not available")

    def initialize_azure_speech(self):
        """Initialize Azure Speech SDK for real-time translation and TTS"""
        if not AZURE_SPEECH_AVAILABLE or not self.azure_speech_key:
            return False

        try:
            # Configure Azure Speech
            self.azure_speech_config = speechsdk.SpeechConfig(
                subscription=self.azure_speech_key,
                region=self.azure_speech_region
            )

            # Set up for real-time translation
            self.azure_speech_config.speech_recognition_language = "en-US"

            # Get target language code
            lang_info = self.get_current_language_info()
            target_lang_code = lang_info['code']

            # Configure translation
            translation_config = speechsdk.translation.SpeechTranslationConfig(
                subscription=self.azure_speech_key,
                region=self.azure_speech_region
            )
            translation_config.speech_recognition_language = "en-US"
            translation_config.add_target_language(target_lang_code)

            # Create translation recognizer
            audio_config = speechsdk.audio.AudioConfig(use_default_microphone=True)
            self.azure_translation_recognizer = speechsdk.translation.TranslationRecognizer(
                translation_config=translation_config,
                audio_config=audio_config
            )

            # Set up event handlers for real-time translation
            self.azure_translation_recognizer.recognizing.connect(self.on_azure_translation_recognizing)
            self.azure_translation_recognizer.recognized.connect(self.on_azure_translation_recognized)

            # Create speech synthesizer for TTS
            self.azure_speech_synthesizer = speechsdk.SpeechSynthesizer(
                speech_config=self.azure_speech_config
            )

            self.log_to_gui("✅ Azure Speech SDK initialized for real-time translation")
            return True

        except Exception as e:
            self.log_to_gui(f"❌ Azure Speech initialization error: {e}")
            return False

    def initialize_openai_realtime(self):
        """Initialize OpenAI Realtime API for streaming translation and TTS"""
        if not OPENAI_AVAILABLE or not self.openai_api_key:
            return False

        try:
            # Configure OpenAI client
            openai.api_key = self.openai_api_key
            self.log_to_gui("✅ OpenAI Realtime API configured")
            return True

        except Exception as e:
            self.log_to_gui(f"❌ OpenAI Realtime initialization error: {e}")
            return False
    
    def setup_audio(self): # Renamed from setup_audio to setup_audio_input for clarity
        """Setup audio input devices"""
        try:
            self.stream = self.audio_instance.open(
                format=self.audio_format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            self.log_to_gui("Audio input initialized successfully")
        except Exception as e:
            self.log_to_gui(f"Audio input setup error: {e}")
            
    def setup_audio_output(self):
        """Setup pygame mixer for audio output. This is primarily for general audio, TTS will use PyAudio."""
        self.log_to_gui("Attempting to set up Pygame audio mixer...")
        try:
            if pygame.mixer.get_init():
                pygame.mixer.quit()
                self.log_to_gui("Pygame mixer quit for re-initialization.")

            # Clear SDL environment variables to ensure Pygame uses its default behavior
            # as we will manage specific device output for TTS via PyAudio.
            if 'SDL_AUDIODRIVER' in os.environ:
                del os.environ['SDL_AUDIODRIVER']
            if 'SDL_AUDIO_DEVICE' in os.environ:
                del os.environ['SDL_AUDIO_DEVICE']
            self.log_to_gui("Cleared SDL audio environment variables.")
            
            pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=1024)
            pygame.mixer.init()
            self.log_to_gui("Pygame audio mixer initialized successfully (using system default).")
            
        except Exception as e:
            self.log_to_gui(f"Pygame audio mixer setup error: {e}. TTS playback will rely solely on PyAudio.")
    
    def calibrate_ambient_noise(self):
        """Measure ambient noise level for voice activity detection"""
        # Ensure PyAudio input stream is open before calibrating
        if not self.stream:
            self.setup_audio() # Re-attempt to set up audio input
        
        if not self.stream: # If still no stream, return default
            return 1000  # Default value if no audio
            
        self.log_to_gui("Measuring ambient noise...")
        noise_samples = []
        
        try:
            for _ in range(int(3 * self.rate / self.chunk_size)):
                chunk = self.stream.read(self.chunk_size, exception_on_overflow=False)
                audio_np = np.frombuffer(chunk, dtype=np.int16).astype(np.float32)
                rms = np.sqrt(np.mean(audio_np**2))
                noise_samples.append(rms)
                time.sleep(0.01)
            
            ambient_level = np.mean(noise_samples)
            self.log_to_gui(f"Ambient noise level: {ambient_level:.2f}")
            return ambient_level
        except Exception as e:
            self.log_to_gui(f"Noise calibration error: {e}")
            return 1000
    
    def calculate_audio_level(self, audio_data):
        """Calculate RMS level of audio data"""
        audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32)
        rms = np.sqrt(np.mean(audio_np**2))
        return rms
    
    def is_voice_detected(self, audio_data):
        """Voice activity detection"""
        current_level = self.calculate_audio_level(audio_data)
        return current_level > self.voice_threshold
    
    def is_silence(self, audio_data):
        """Check if audio is silence/background noise"""
        current_level = self.calculate_audio_level(audio_data)
        return current_level < self.silence_threshold
    
    def audio_to_numpy(self, audio_data):
        """Convert audio data to numpy array for Whisper"""
        audio_np = np.frombuffer(audio_data, dtype=np.int16)
        audio_np = audio_np.astype(np.float32) / 32768.0
        return audio_np
    
    def process_text_input(self, text):
        """Process text input (typing mode) - now asynchronous for better responsiveness"""
        if not text or len(text.strip()) < 2:
            return

        if not self.typing_mode_active:
            return

        text = text.strip()
        self.log_to_gui(f"🗣️ English: {text}")

        # Process translation asynchronously for immediate UI response
        self.executor.submit(self._process_text_translation, text)

    def _process_text_translation(self, text):
        """Internal method to handle text translation in background thread"""
        try:
            start_time = time.time()

            # Fast translation
            translated_text = self.translate_fast(text)

            translation_time = time.time() - start_time

            if translated_text:
                lang_info = self.get_current_language_info()
                flag = self.get_language_flag(self.target_language)
                self.log_to_gui(f"{flag} {self.target_language}: {translated_text} (⚡ {translation_time:.2f}s)")
                self.queue_tts(translated_text)
            else:
                self.log_to_gui("❌ Translation failed")
        except Exception as e:
            self.log_to_gui(f"Translation error: {e}")
    
    def get_language_flag(self, language):
        """Get flag emoji for language"""
        flags = {
            'Japanese': '🇯🇵',
            'Spanish': '🇪🇸',
            'French': '🇫🇷',
            'German': '🇩🇪',
            'Italian': '🇮🇹',
            'Portuguese': '🇵🇹',
            'Russian': '🇷🇺',
            'Chinese (Simplified)': '🇨🇳',
            'Chinese (Traditional)': '🇹🇼',
            'Korean': '🇰🇷',
            'Hindi': '🇮🇳',
            'Arabic': '🇸🇦',
            'Dutch': '🇳🇱',
            'Swedish': '🇸🇪',
            'Norwegian': '🇳🇴',
            'Danish': '🇩🇰',
            'Finnish': '🇫🇮',
            'Polish': '🇵🇱',
            'Czech': '🇨🇿',
            'Hungarian': '🇭🇺',
            'Romanian': '🇷🇴',
            'Greek': '🇬🇷',
            'Turkish': '🇹🇷',
            'Hebrew': '🇮🇱',
            'Thai': '🇹🇭',
            'Vietnamese': '🇻🇳',
            'Indonesian': '🇮🇩',
            'Malay': '🇲🇾',
            'Filipino': '🇵🇭',
            'Ukrainian': '🇺🇦',
            'Bulgarian': '🇧🇬',
            'Croatian': '🇭🇷',
            'Serbian': '🇷🇸',
            'Slovak': '🇸🇰',
            'Slovenian': '🇸🇮',
            'Lithuanian': '🇱🇹',
            'Latvian': '🇱🇻',
            'Estonian': '🇪🇪',
            'English': '🇬🇧', # Added English flag
        }
        return flags.get(language, '🌍')
    
    def process_complete_speech(self, audio_data):
        """Process a complete speech segment with performance monitoring"""
        if self.is_processing or not self.voice_mode_active:
            return

        self.is_processing = True
        total_start_time = time.time()

        try:
            self.log_to_gui("🎯 Processing complete speech...")

            audio_np = self.audio_to_numpy(audio_data)

            if len(audio_np) < self.rate * 0.5:
                self.log_to_gui("⚠️ Speech too short, skipping...")
                return

            # Transcription timing
            transcription_start = time.time()

            # Use RealtimeSTT for transcription
            # Convert audio to PCM format for RealtimeSTT
            audio_pcm = (audio_np * 32767).astype(np.int16).tobytes()

            # Feed audio to RealtimeSTT and get transcription
            self.realtime_recorder.feed_audio(audio_pcm)
            text = self.realtime_recorder.text()

            if text is None:
                text = ""
            text = text.strip()

            transcription_time = time.time() - transcription_start
            self.performance_stats['transcription_times'].append(transcription_time)

            if text and len(text) > 3 and len(text.split()) > 1:
                self.log_to_gui(f"🗣️ English: {text} (🎯 {transcription_time:.2f}s)")

                # Translation timing
                translation_start = time.time()
                translated_text = self.translate_fast(text)
                translation_time = time.time() - translation_start
                self.performance_stats['translation_times'].append(translation_time)

                if translated_text:
                    flag = self.get_language_flag(self.target_language)
                    total_time = time.time() - total_start_time
                    self.performance_stats['total_processing_times'].append(total_time)

                    avg_transcription = sum(self.performance_stats['transcription_times']) / len(self.performance_stats['transcription_times'])
                    avg_translation = sum(self.performance_stats['translation_times']) / len(self.performance_stats['translation_times'])

                    self.log_to_gui(f"{flag} {self.target_language}: {translated_text}")

                    self.queue_tts(translated_text)
            else:
                self.log_to_gui("⚠️ Skipping unclear speech")
                    
        except Exception as e:
            self.log_to_gui(f"Processing error: {e}")
        finally:
            self.is_processing = False

    def get_performance_stats(self):
        """Get current performance statistics"""
        stats = {}
        for key, times in self.performance_stats.items():
            if times:
                stats[key] = {
                    'avg': sum(times) / len(times),
                    'min': min(times),
                    'max': max(times),
                    'count': len(times)
                }
            else:
                stats[key] = {'avg': 0, 'min': 0, 'max': 0, 'count': 0}
        return stats
    
    def translate_fast(self, text):
        """Fast translation with caching"""
        cache_key = f"{text}_{self.target_language}"
        if cache_key in self.translation_cache:
            return self.translation_cache[cache_key]

        if self.translation_service == 'Google Translate':
            try:
                lang_info = self.get_current_language_info()
                result = self.translator.translate(text, src='en', dest=lang_info['code'])
                translated_text = result.text
                
                # Cache shorter translations
                if len(text.split()) <= 15:
                    self.translation_cache[cache_key] = translated_text
                return translated_text
            except Exception as e:
                self.log_to_gui(f"Google Translate error: {e}")
                return None
        elif self.translation_service == 'Ollama':
            return self.translate_with_ollama(text)
        elif self.translation_service == 'GPT':
            return self.translate_with_gpt(text)
        else:
            self.log_to_gui(f"Unknown translation service: {self.translation_service}")
            return None

    def initialize_ollama(self):
        """Initialize or reset the Ollama conversation history."""
        self.log_to_gui("🤖 Initializing Ollama...")
        self.ollama_messages = []  # Clear history
        
        lang_info = self.get_current_language_info()
        target_language_name = self.target_language
        
        system_prompt = f"""Translate the following English text to {target_language_name}. Your response must contain ONLY the translated text. Do not say any other words. Do not include explanations, commentary, or any English text in your response. Translate EXACTLY what is provided into the EXACT language specified. Preserve the original tone, emotions, intent, and any informal or expressive language. Match the energy and nuance of the input as accurately as possible. The output must be grammatically correct, fluent, and sound like a native speaker would say naturally. Never ask questions, even if the input sounds like a question. You MUST translate everything directly."""
        
        self.ollama_messages.append({"role": "system", "content": system_prompt})
        
        # Send a test message to initialize the context
        init_messages = self.ollama_messages + [{"role": "user", "content": "Understood."}]
        
        url = "http://localhost:11434/api/chat"
        data = {
            "model": "gemma3:12b",
            "messages": init_messages,
            "stream": False,
            "keep_alive": -1
        }
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            response_data = response.json()
            initial_response = response_data['message']['content'].strip()
            
            self.ollama_messages.append({"role": "assistant", "content": initial_response})
            self.log_to_gui(f"🤖 Ollama initialized. Response: {initial_response}")
            
        except requests.exceptions.RequestException as e:
            self.log_to_gui(f"Ollama connection error during initialization: {e}")
        except Exception as e:
            self.log_to_gui(f"Ollama initialization error: {e}")
            
    def initialize_gpt(self):
        """Initialize the g4f GPT client."""
        self.log_to_gui("🤖 Initializing GPT (g4f)...")
        try:
            self.gpt_client = Client()
            self.log_to_gui("🤖 GPT (g4f) client initialized.")
        except Exception as e:
            self.log_to_gui(f"GPT (g4f) client initialization error: {e}")

    def translate_with_ollama(self, text):
        """Translate text using Ollama"""
        if not self.ollama_messages:
            self.initialize_ollama()

        lang_info = self.get_current_language_info()
        target_language_name = self.target_language
        
        system_prompt = f"""Translate the following English text to {target_language_name}. Your response must contain ONLY the translated text. Do not say any other words. Do not include explanations, commentary, or any English text in your response. Translate EXACTLY what is provided into the EXACT language specified. Preserve the original tone, emotions, intent, and any informal or expressive language. Match the energy and nuance of the input as accurately as possible. The output must be grammatically correct, fluent, and sound like a native speaker would say naturally. Never ask questions, even if the input sounds like a question. You MUST translate everything directly."""

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": text}]
        
        url = "http://localhost:11434/api/chat"
        data = {
            "model": "gemma3:12b",  # Or make this configurable
            "messages": messages,
            "stream": False,
            "keep_alive": -1
        }

        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            
            response_data = response.json()
            translated_text = response_data['message']['content'].strip()
            
            # Cache result
            cache_key = f"{text}_{self.target_language}"
            self.translation_cache[cache_key] = translated_text
            
            return translated_text
            
        except requests.exceptions.RequestException as e:
            self.log_to_gui(f"Ollama connection error: {e}")
            return None
        except Exception as e:
            self.log_to_gui(f"Ollama translation error: {e}")
            return None
            
    def translate_with_gpt(self, text):
        """Translate text using g4f GPT provider"""
        if not self.gpt_client:
            self.initialize_gpt()
            if not self.gpt_client: # If initialization failed, cannot proceed
                self.log_to_gui("❌ GPT client not initialized, cannot translate.")
                return None

        lang_info = self.get_current_language_info()
        target_language_name = self.target_language
        
        system_prompt = f"""Translate the following English text to {target_language_name}. Your response must contain ONLY the translated text. Do not say any other words. Do not include explanations, commentary, or any English text in your response. Translate EXACTLY what is provided into the EXACT language specified. Preserve the original tone, emotions, intent, and any informal or expressive language. Match the energy and nuance of the input as accurately as possible. The output must be grammatically correct, fluent, and sound like a native speaker would say naturally. Never ask questions, even if the input sounds like a question. You MUST translate everything directly."""

        messages = [{"role": "system", "content": system_prompt}, {"role": "user", "content": text}]
        
        try:
            stream = self.gpt_client.chat.completions.create(
                model="gpt-4o", # Or make this configurable
                messages=messages,
                stream=True
            )
            
            translated_text_parts = []
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    translated_text_parts.append(chunk.choices[0].delta.content)
            
            translated_text = "".join(translated_text_parts).strip()
            
            # Cache result
            cache_key = f"{text}_{self.target_language}"
            self.translation_cache[cache_key] = translated_text
            
            return translated_text
            
        except Exception as e:
            self.log_to_gui(f"GPT translation error: {e}")
            return None
    
    def queue_tts(self, translated_text):
        """Add TTS to generation queue for pre-processing"""
        if translated_text and translated_text.strip():
            text = str(translated_text).strip()
            # Add to generation queue first (for pre-processing)
            self.tts_generation_queue.put({
                'text': text,
                'voice': self.languages[self.target_language]['voice'],
                'timestamp': time.time()
            })
            generation_queue_size = self.tts_generation_queue.qsize()
            playback_queue_size = self.tts_queue.qsize()
            total_queue = generation_queue_size + playback_queue_size
            if total_queue > 1:
                self.log_to_gui(f"🔊 Queued for TTS (#{total_queue} in queue)")
        else:
            self.log_to_gui("🔊 Playing immediately...")

    def tts_generation_worker(self):
        """TTS generation worker thread - pre-generates audio and queues for playback"""
        while self.is_running:
            try:
                if not self.tts_generation_queue.empty():
                    item = self.tts_generation_queue.get(timeout=1)
                    if item and isinstance(item, dict):
                        text = item.get('text', '')
                        voice = item.get('voice', '')
                        priority = item.get('priority', 'normal')

                        if text:
                            # Pre-generate the TTS audio
                            audio_data = self.generate_tts_audio(text)
                            if audio_data:
                                # Add the pre-generated audio to the playback queue
                                # Include priority information for playback ordering
                                playback_item = (text, audio_data, priority)
                                self.tts_queue.put(playback_item)
                        self.tts_generation_queue.task_done()
                else:
                    time.sleep(0.1)
            except Exception as e:
                if self.is_running:
                    self.log_to_gui(f"TTS generation worker error: {e}")
                time.sleep(0.1)

    def tts_worker(self):
        """Worker thread that processes TTS queue sequentially with pre-generated audio"""
        while self.tts_worker_running:
            try:
                try:
                    item = self.tts_queue.get(timeout=1.0)
                except:
                    continue  # Timeout, check if still running

                if item:
                    if isinstance(item, tuple) and len(item) >= 2:
                        # Pre-generated audio (with optional priority)
                        if len(item) == 3:
                            text, audio_data, priority = item
                        else:
                            text, audio_data = item
                            priority = 'normal'

                        # For high-priority real-time items, play immediately
                        if priority == 'high':
                            self.log_to_gui(f"⚡ Real-time TTS: {text}")

                        self.play_tts_audio(text, audio_data)
                    else:
                        # Fallback for direct text (shouldn't happen with new system)
                        self.play_tts_sync(item)
                    self.tts_queue.task_done()

            except Exception as e:
                if self.tts_worker_running:
                    self.log_to_gui(f"TTS worker error: {e}")

    def generate_tts_audio(self, text):
        """Generate TTS audio data without playing it using Edge TTS or Google TTS"""
        try:
            # Get current language info
            lang_info = self.get_current_language_info()
            voice = lang_info['voice']
            tts_engine = lang_info.get('tts_engine', 'edge')

            # Create cache key
            cache_key = f"{text}_{voice}_{tts_engine}"

            # Check if audio is already cached
            if cache_key in self.tts_cache:
                return self.tts_cache[cache_key]

            # Generate new TTS audio
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_filename = temp_file.name

            # Generate TTS audio based on engine
            if tts_engine == 'edge':
                # Use Edge TTS
                async def generate_edge_tts():
                    communicate = edge_tts.Communicate(text, voice)
                    await communicate.save(temp_filename)

                # Run Edge TTS generation
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(generate_edge_tts())
                finally:
                    loop.close()

            elif tts_engine == 'gtts':
                # Use Google TTS
                try:
                    # voice contains the language code for Google TTS
                    lang_code = voice
                    tts = gTTS(text=text, lang=lang_code, slow=False)
                    tts.save(temp_filename)
                except Exception as gtts_error:
                    self.log_to_gui(f"Google TTS error: {gtts_error}")
                    return None

            # Load and process audio
            if os.path.exists(temp_filename):
                try:
                    # Load MP3 with pydub and convert to raw PCM
                    audio_segment = AudioSegment.from_mp3(temp_filename)

                    # Ensure 16-bit stereo at 44.1kHz for broad compatibility
                    audio_segment = audio_segment.set_frame_rate(44100).set_channels(2).set_sample_width(2)

                    raw_audio_data = audio_segment.raw_data
                    audio_format = self.audio_instance.get_format_from_width(audio_segment.sample_width)
                    channels = audio_segment.channels
                    rate = audio_segment.frame_rate

                    audio_data = (raw_audio_data, audio_format, channels, rate)

                    # Cache the audio data (limit cache size)
                    if len(self.tts_cache) >= self.max_tts_cache_size:
                        # Remove oldest entry
                        oldest_key = next(iter(self.tts_cache))
                        del self.tts_cache[oldest_key]

                    self.tts_cache[cache_key] = audio_data

                    # Log which TTS engine was used
                    engine_name = "Edge TTS" if tts_engine == 'edge' else "Google TTS"
                    self.log_to_gui(f"🎵 Generated with {engine_name}")

                    return audio_data

                finally:
                    # Clean up temp file
                    try:
                        os.unlink(temp_filename)
                    except Exception:
                        pass

            return None

        except Exception as e:
            self.log_to_gui(f"TTS generation error: {e}")
            return None

    def play_tts_audio(self, text, audio_data):
        """Play pre-generated TTS audio"""
        with self.tts_lock:
            try:
                self.is_tts_playing = True
                start_time = time.time()

                self.log_to_gui(f"🔊 Playing: {text}")

                raw_audio_data, audio_format, channels, rate = audio_data

                # Open PyAudio stream for playback
                output_stream = self.audio_instance.open(
                    format=audio_format,
                    channels=channels,
                    rate=rate,
                    output=True,
                    output_device_index=self.output_device_index
                )

                # Play the audio in chunks for better responsiveness
                chunk_size = 4096
                for i in range(0, len(raw_audio_data), chunk_size):
                    chunk = raw_audio_data[i:i+chunk_size]
                    output_stream.write(chunk)

                # Wait for playback to finish
                output_stream.stop_stream()
                output_stream.close()

                total_time = time.time() - start_time
                self.log_to_gui(f"✅ Playback finished (⚡ {total_time:.2f}s)")

            except Exception as e:
                self.log_to_gui(f"TTS playback error: {e}")
            finally:
                self.is_tts_playing = False

    def play_tts_sync(self, translated_text):
        """Play TTS synchronously using cached audio files for better performance"""
        with self.tts_lock:
            try:
                self.is_tts_playing = True
                start_time = time.time()

                # Get current language voice
                lang_info = self.get_current_language_info()
                voice = lang_info['voice']

                # Create cache key
                cache_key = f"{translated_text}_{voice}"

                # Check if audio is already cached
                if cache_key in self.tts_cache:
                    self.log_to_gui(f"🔊 Playing cached: {translated_text}")
                    raw_audio_data, audio_format, channels, rate = self.tts_cache[cache_key]
                else:
                    self.log_to_gui(f"🔊 Generating TTS: {translated_text}")

                    # Create a temporary file for the audio
                    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                        temp_filename = temp_file.name

                    # Generate TTS audio and save to temp file
                    async def generate_tts():
                        communicate = edge_tts.Communicate(translated_text, voice)
                        await communicate.save(temp_filename)

                    # Run TTS generation
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(generate_tts())
                    finally:
                        loop.close()

                    # Load and process audio
                    if os.path.exists(temp_filename):
                        try:
                            # Load MP3 with pydub and convert to raw PCM
                            audio_segment = AudioSegment.from_mp3(temp_filename)

                            # Ensure 16-bit stereo at 44.1kHz for broad compatibility
                            audio_segment = audio_segment.set_frame_rate(44100).set_channels(2).set_sample_width(2)

                            raw_audio_data = audio_segment.raw_data
                            audio_format = self.audio_instance.get_format_from_width(audio_segment.sample_width)
                            channels = audio_segment.channels
                            rate = audio_segment.frame_rate

                            # Cache the audio data (limit cache size)
                            if len(self.tts_cache) >= self.max_tts_cache_size:
                                # Remove oldest entry
                                oldest_key = next(iter(self.tts_cache))
                                del self.tts_cache[oldest_key]

                            self.tts_cache[cache_key] = (raw_audio_data, audio_format, channels, rate)

                        finally:
                            # Clean up temp file
                            try:
                                os.unlink(temp_filename)
                            except Exception as unlink_error:
                                self.log_to_gui(f"Error deleting temp file: {unlink_error}")
                    else:
                        self.log_to_gui("❌ TTS file generation failed")
                        return

                # Play the audio
                try:
                    # Open PyAudio stream for playback
                    output_stream = self.audio_instance.open(
                        format=audio_format,
                        channels=channels,
                        rate=rate,
                        output=True,
                        output_device_index=self.output_device_index # Use selected device
                    )

                    # Play the audio in chunks for better responsiveness
                    chunk_size = 4096
                    for i in range(0, len(raw_audio_data), chunk_size):
                        chunk = raw_audio_data[i:i+chunk_size]
                        output_stream.write(chunk)

                    # Wait for playback to finish
                    output_stream.stop_stream()
                    output_stream.close()

                    total_time = time.time() - start_time
                    self.log_to_gui(f"✅ Playback finished (⚡ {total_time:.2f}s)")

                except Exception as play_error:
                    self.log_to_gui(f"PyAudio playback error: {play_error}")

            except Exception as e:
                self.log_to_gui(f"TTS error: {e}")
            finally:
                self.is_tts_playing = False
    
    def switch_to_voice_mode(self):
        """Switch to voice mode using RealtimeSTT"""
        self.mode = "voice"
        self.typing_mode_active = False

        if self.use_realtime_stt:
            self.log_to_gui("🎤 Switching to Voice Mode with RealtimeSTT...")
            self.voice_mode_active = True

            # Start RealtimeSTT listening thread
            if not hasattr(self, 'realtime_listen_thread') or not self.realtime_listen_thread.is_alive():
                self.realtime_listen_thread = threading.Thread(target=self.realtime_listen_loop, daemon=True)
                self.realtime_listen_thread.start()

            self.log_to_gui("🎧 Voice mode activated with RealtimeSTT - Listening for speech...")
        else:
            # Fallback to PyAudio mode
            if not self.stream:
                self.setup_audio()

            if self.stream:
                self.log_to_gui("🎤 Switching to Voice Mode...")
                self.log_to_gui("Calibrating ambient noise... Please be quiet for 3 seconds...")
                self.ambient_noise_level = self.calibrate_ambient_noise()
                self.voice_threshold = self.ambient_noise_level * 3.0
                self.silence_threshold = self.ambient_noise_level * 1.5
                self.log_to_gui(f"Voice threshold: {self.voice_threshold:.2f}")
                self.log_to_gui(f"Silence threshold: {self.silence_threshold:.2f}")

                self.voice_mode_active = True

                # Start or restart listening thread
                if not hasattr(self, 'listen_thread') or not self.listen_thread.is_alive():
                    self.listen_thread = threading.Thread(target=self.continuous_listen, daemon=True)
                    self.listen_thread.start()

                self.log_to_gui("🎧 Voice mode activated - Listening for speech...")
            else:
                self.log_to_gui("❌ Could not activate voice mode - audio device error")
    
    def switch_to_typing_mode(self):
        """Switch to typing mode"""
        self.mode = "typing"
        self.voice_mode_active = False
        self.typing_mode_active = True
        
        # Reset speech buffer when switching modes
        self.speech_buffer = b""
        self.is_speaking = False
        
        self.log_to_gui("⌨️ Switched to Typing Mode - Type your messages!")

    def realtime_listen_loop(self):
        """Main listening loop using RealtimeSTT"""
        self.log_to_gui("🎧 RealtimeSTT listening loop started...")

        # Track speaking state for user feedback
        self.is_speaking_realtime = False

        def process_transcription(text):
            """Callback function for RealtimeSTT transcription"""
            if text and text.strip() and len(text.strip()) > 3:
                text = text.strip()
                self.log_to_gui(f"🗣️ English: {text}")

                # Process the transcription (translate and speak)
                threading.Thread(target=self.process_transcription_async, args=(text,), daemon=True).start()

        # Set up RealtimeSTT callbacks (using correct API)
        # Note: RealtimeSTT callbacks are set during initialization, not after
        # We'll handle speaking detection through the text callback instead

        try:
            last_status_time = time.time()
            while self.voice_mode_active and self.is_running:
                try:
                    # Use RealtimeSTT to get transcription
                    self.realtime_recorder.text(process_transcription)

                    # Periodic status check (every 30 seconds)
                    current_time = time.time()
                    if current_time - last_status_time > 30:
                        self.log_to_gui("🔄 RealtimeSTT status: Active and listening...")
                        last_status_time = current_time

                except Exception as e:
                    self.log_to_gui(f"RealtimeSTT error: {e}")
                    # Try to restart RealtimeSTT if it fails
                    try:
                        self.log_to_gui("🔄 Attempting to restart RealtimeSTT...")
                        self.realtime_recorder.shutdown()
                        time.sleep(1)
                        # Reinitialize with same settings including callbacks
                        device = "cuda" if torch.cuda.is_available() else "cpu"
                        compute_type = "float16" if torch.cuda.is_available() else "float32"

                        def on_recording_start():
                            """Called when RealtimeSTT starts recording"""
                            self.log_to_gui("🎤 Started speaking...")

                        def on_recording_stop():
                            """Called when RealtimeSTT stops recording"""
                            self.log_to_gui("⏸️ Pause detected, processing speech...")

                        def on_realtime_transcription_update(text):
                            """Called for every real-time transcription update (partial text)"""
                            if text and text.strip():
                                self.log_to_gui(f"🔄 Live: {text}")
                                # Process individual words for immediate translation and TTS
                                self.process_realtime_words(text)

                        def on_realtime_transcription_stabilized(text):
                            """Called when real-time transcription is stabilized (more accurate)"""
                            if text and text.strip():
                                self.log_to_gui(f"✅ Stable: {text}")

                        self.realtime_recorder = AudioToTextRecorder(
                            model="medium",
                            language="en",
                            device=device,
                            compute_type=compute_type,
                            use_microphone=True,
                            spinner=False,
                            level=30,
                            ensure_sentence_starting_uppercase=True,
                            ensure_sentence_ends_with_period=True,
                            post_speech_silence_duration=0.8,
                            min_length_of_recording=0.5,
                            min_gap_between_recordings=0.3,
                            silero_sensitivity=0.6,
                            webrtc_sensitivity=3,
                            silero_use_onnx=True,
                            beam_size=1,
                            batch_size=16 if torch.cuda.is_available() else 8,
                            on_recording_start=on_recording_start,
                            on_recording_stop=on_recording_stop,
                            # Enable real-time transcription
                            enable_realtime_transcription=True,
                            use_main_model_for_realtime=False,
                            realtime_model_type="base",
                            realtime_processing_pause=0.1,
                            on_realtime_transcription_update=on_realtime_transcription_update,
                            on_realtime_transcription_stabilized=on_realtime_transcription_stabilized,
                            realtime_batch_size=8 if torch.cuda.is_available() else 4,
                            beam_size_realtime=1
                        )
                        self.log_to_gui("✅ RealtimeSTT restarted successfully")
                    except Exception as restart_error:
                        self.log_to_gui(f"❌ Failed to restart RealtimeSTT: {restart_error}")
                        time.sleep(5)  # Wait longer before trying again

        except Exception as e:
            self.log_to_gui(f"RealtimeSTT listening error: {e}")

        self.log_to_gui("🛑 RealtimeSTT listening loop stopped")

    def process_realtime_words(self, text):
        """Enhanced real-time transcription processing with streaming translation"""
        if not text or not text.strip():
            return

        # Initialize tracking variables
        if not hasattr(self, 'last_realtime_text'):
            self.last_realtime_text = ""
        if not hasattr(self, 'realtime_buffer'):
            self.realtime_buffer = ""
        if not hasattr(self, 'last_translation_time'):
            self.last_translation_time = 0

        current_time = time.time()

        # Enhanced real-time processing with phrase-based translation
        if text != self.last_realtime_text:
            # Check if we have enough new content for translation
            if len(text.split()) >= 3 and (current_time - self.last_translation_time) > 0.5:
                # Process phrases instead of individual words for better context
                self.process_realtime_phrase(text)
                self.last_translation_time = current_time
            elif len(text.split()) >= 1:
                # For single words, use immediate processing
                new_words = text.replace(self.last_realtime_text, "").strip().split()
                for word in new_words:
                    if word and len(word) > 2:  # Skip very short words
                        self.process_single_word_realtime(word)

            self.last_realtime_text = text

    def process_realtime_phrase(self, text):
        """Process phrases for better contextual translation"""
        try:
            if not text or len(text.strip()) < 3:
                return

            # Use fast translation for phrases
            translated_text = self.translate_fast(text)
            if translated_text and translated_text != text:
                flag = self.get_language_flag(self.target_language)
                self.log_to_gui(f"⚡ Realtime: {text} → {translated_text}")

                # Queue for immediate TTS with high priority
                self.queue_realtime_tts(translated_text, priority='high')

        except Exception as e:
            # Silent error handling for realtime processing
            pass

    def process_single_word_realtime(self, word):
        """Enhanced single word processing with caching"""
        try:
            # Clean the word
            word = word.strip().rstrip('.,!?;:')
            if not word or len(word) < 2:
                return

            # Skip if target language is English
            if self.target_language == "English":
                return

            # Check cache first for instant response
            cache_key = f"word_{word}_{self.target_language}"
            if cache_key in self.translation_cache:
                translated_word = self.translation_cache[cache_key]
                self.log_to_gui(f"⚡ {word} → {translated_word}")
                self.queue_realtime_tts(translated_word, priority='medium')
                return

            # Translate the single word
            translated_word = self.translate_fast(word)
            if translated_word and translated_word != word:
                # Cache the result
                self.translation_cache[cache_key] = translated_word

                self.log_to_gui(f"⚡ {word} → {translated_word}")
                self.queue_realtime_tts(translated_word, priority='medium')

        except Exception as e:
            # Silent error handling for realtime processing
            pass

    def queue_realtime_tts(self, text, priority='high'):
        """Enhanced real-time TTS queueing with priority levels and streaming"""
        if not text or not text.strip():
            return

        try:
            # Get current language voice
            lang_info = self.get_current_language_info()
            voice = lang_info['voice']

            # Create TTS item with priority
            tts_item = {
                'text': str(text).strip(),
                'voice': voice,
                'priority': priority,
                'timestamp': time.time()
            }

            # For high priority items, try to interrupt current TTS if needed
            if priority == 'high':
                # Clear lower priority items from queue to prioritize real-time content
                self.clear_low_priority_tts()

            # Add to generation queue
            try:
                self.tts_generation_queue.put(tts_item, block=False)
            except:
                # If queue is full, remove oldest low-priority item and try again
                if priority == 'high':
                    self.clear_oldest_tts_item()
                    try:
                        self.tts_generation_queue.put(tts_item, block=False)
                    except:
                        pass  # Skip if still can't queue

        except Exception as e:
            pass  # Silent error handling

    def clear_low_priority_tts(self):
        """Clear low priority TTS items to make room for real-time content"""
        try:
            # Create a new queue with only high priority items
            temp_queue = Queue()
            while not self.tts_generation_queue.empty():
                try:
                    item = self.tts_generation_queue.get_nowait()
                    if item.get('priority') == 'high':
                        temp_queue.put(item)
                except:
                    break

            # Replace the queue
            self.tts_generation_queue = temp_queue
        except Exception as e:
            pass

    def clear_oldest_tts_item(self):
        """Remove the oldest item from TTS queue"""
        try:
            if not self.tts_generation_queue.empty():
                self.tts_generation_queue.get_nowait()
        except:
            pass

    def process_transcription_async(self, text):
        """Process transcription asynchronously"""
        try:
            # Translation timing
            translation_start = time.time()
            translated_text = self.translate_fast(text)
            translation_time = time.time() - translation_start

            self.performance_stats['translation_times'].append(translation_time)

            if translated_text:
                self.log_to_gui(f"🌐 {self.target_language}: {translated_text} (⚡ {translation_time:.2f}s)")

                # Add to TTS generation queue for pre-generation
                # Ensure text is a string
                text_for_tts = str(translated_text).strip()
                if text_for_tts:
                    self.tts_generation_queue.put({
                        'text': text_for_tts,
                        'voice': self.languages[self.target_language]['voice'],
                        'timestamp': time.time(),
                        'priority': 'normal'  # Regular translations have normal priority
                    })
            else:
                self.log_to_gui("⚠️ Translation failed")

        except Exception as e:
            self.log_to_gui(f"Transcription processing error: {e}")

    def continuous_listen(self):
        """Voice capture with optimized real-time pause detection (PyAudio fallback)"""
        # Skip if using RealtimeSTT
        if self.use_realtime_stt:
            self.log_to_gui("🎧 Using RealtimeSTT - PyAudio continuous_listen disabled")
            return

        current_time = time.time()
        last_process_time = current_time

        while self.is_running:
            try:
                # Only listen when voice mode is active
                if not self.voice_mode_active or self.mode != "voice":
                    time.sleep(0.05)  # Reduced sleep for better responsiveness
                    continue

                if not self.stream:
                    break

                chunk = self.stream.read(self.chunk_size, exception_on_overflow=False)
                current_time = time.time()

                # Add to rolling buffer for real-time processing
                audio_np = np.frombuffer(chunk, dtype=np.int16)
                self.audio_buffer.extend(audio_np)

                has_voice = self.is_voice_detected(chunk)
                is_quiet = self.is_silence(chunk)

                if has_voice:
                    if not self.is_speaking:
                        self.log_to_gui("🎤 Started speaking...")
                        self.is_speaking = True
                        self.speech_buffer = b""

                    self.speech_buffer += chunk
                    self.last_voice_time = current_time
                    self.silence_duration = 0

                    max_buffer_size = int(self.rate * self.max_speech_duration * 2)
                    if len(self.speech_buffer) > max_buffer_size:
                        self.log_to_gui("🔄 Buffer full, processing...")
                        self.executor.submit(self.process_complete_speech, self.speech_buffer)
                        self.speech_buffer = b""
                        self.is_speaking = False

                elif self.is_speaking:
                    self.speech_buffer += chunk
                    self.silence_duration = current_time - self.last_voice_time

                    speech_duration = len(self.speech_buffer) / (self.rate * 2)

                    if (self.silence_duration >= self.pause_threshold and
                        speech_duration >= self.min_speech_duration):

                        self.log_to_gui(f"⏸️ Pause detected ({self.silence_duration:.1f}s), processing speech...")
                        self.executor.submit(self.process_complete_speech, self.speech_buffer)
                        self.speech_buffer = b""
                        self.is_speaking = False
                        self.silence_duration = 0

                # Real-time processing for very short utterances
                elif (current_time - last_process_time >= self.processing_interval and
                      len(self.audio_buffer) > self.rate * 0.5):  # At least 0.5 seconds of audio

                    # Check if there's been recent voice activity
                    if self.last_voice_time > 0 and (current_time - self.last_voice_time) < 2.0:
                        # Process recent audio buffer for quick responses
                        buffer_audio = np.array(list(self.audio_buffer), dtype=np.int16)
                        buffer_bytes = buffer_audio.tobytes()

                        if len(buffer_bytes) > self.rate * self.min_speech_duration * 2:
                            self.executor.submit(self.process_realtime_audio, buffer_bytes)

                    last_process_time = current_time

            except Exception as e:
                self.log_to_gui(f"Audio capture error: {e}")
                time.sleep(0.05)  # Reduced sleep time

    def process_realtime_audio(self, audio_data):
        """Process audio in real-time for quick responses"""
        if self.is_processing:
            return  # Skip if already processing

        try:
            audio_np = self.audio_to_numpy(audio_data)

            # Only process if we have enough audio and it's not too long
            if len(audio_np) < self.rate * 0.3 or len(audio_np) > self.rate * 3.0:
                return

            # Quick transcription with RealtimeSTT
            # Convert audio to PCM format for RealtimeSTT
            audio_pcm = (audio_np * 32767).astype(np.int16).tobytes()

            # Feed audio to RealtimeSTT and get transcription
            self.realtime_recorder.feed_audio(audio_pcm)
            text = self.realtime_recorder.text()

            if text and text.strip():
                text = text.strip()
                if len(text) > 5 and len(text.split()) > 1:
                    self.log_to_gui(f"⚡ Real-time: {text}")
                    # Don't process the same text again in the main pipeline

        except Exception as e:
            # Silently ignore real-time processing errors to avoid spam
            pass
    
    def stop(self):
        """Stop the translator"""
        self.log_to_gui("🛑 Stopping translator...")
        self.is_running = False
        self.tts_worker_running = False
        self.voice_mode_active = False
        self.typing_mode_active = False
        
        # Shutdown RealtimeSTT
        if hasattr(self, 'realtime_recorder') and self.realtime_recorder:
            try:
                self.realtime_recorder.shutdown()
                self.log_to_gui("RealtimeSTT shutdown.")
            except Exception as e:
                self.log_to_gui(f"Error shutting down RealtimeSTT: {e}")

        # Wait for TTS queue to finish current item
        if not self.tts_queue.empty():
            self.log_to_gui("⏳ Finishing queued TTS...")
            time.sleep(1)  # Give it a moment to finish current playback
        
        # Stop pygame mixer (if it was initialized)
        try:
            if pygame.mixer.get_init():
                pygame.mixer.music.stop()
                pygame.mixer.quit()
                self.log_to_gui("Pygame mixer quit.")
        except Exception as e:
            self.log_to_gui(f"Error quitting Pygame mixer: {e}")
        
        # Close audio input stream
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
                self.log_to_gui("Audio input stream closed.")
            except Exception as e:
                self.log_to_gui(f"Error closing audio input stream: {e}")
        
        # Terminate PyAudio instance
        if self.audio_instance:
            try:
                self.audio_instance.terminate()
                self.log_to_gui("PyAudio instance terminated.")
            except Exception as e:
                self.log_to_gui(f"Error terminating PyAudio instance: {e}")
        
        # Shutdown executor
        self.executor.shutdown(wait=False)
        self.log_to_gui("✅ Translator stopped")

class TranslatorGUI:
    def __init__(self):
        # Create main window
        self.root = ctk.CTk()
        self.root.title("🎯 Whisper Real-Time Translator")
        self.root.geometry("900x750")
        self.root.resizable(False, False) # Make window non-resizable
        
        # Configure grid weights
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=1)
        
        # Initialize translator
        self.translator = WhisperRealTimeTranslator(gui_callback=self.log_message)
        
        self.setup_gui()
        self.setup_styles()
        
    def setup_gui(self):
        """Setup the GUI components"""
        
        # Title Frame
        title_frame = ctk.CTkFrame(self.root)
        title_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))
        title_frame.grid_columnconfigure(0, weight=1) # Column for title
        title_frame.grid_columnconfigure(1, weight=1) # Column for mode
        title_frame.grid_columnconfigure(2, weight=1) # Column for output device
        title_frame.grid_columnconfigure(3, weight=1) # Column for translator service
        title_frame.grid_columnconfigure(4, weight=1) # Column for target language
        
        # Title
        title_label = ctk.CTkLabel(
            title_frame,
            text="🎯 Whisper Real-Time Translator",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=20, sticky="w")
        
        # Output Device Selection (Near top right)
        output_device_label = ctk.CTkLabel(title_frame, text="Output Device:", font=ctk.CTkFont(size=16))
        output_device_label.grid(row=0, column=2, padx=(20, 10), pady=10, sticky="e")
        
        output_devices = [name for index, name in self.translator.get_output_devices()]
        output_devices.insert(0, "None (System Default)") # Add None option
        
        self.output_device_var = ctk.StringVar(value="None (System Default)")
        self.output_device_combo = ctk.CTkComboBox(
            title_frame,
            values=output_devices,
            variable=self.output_device_var,
            command=self.on_output_device_change,
            font=ctk.CTkFont(size=14),
            width=200
        )
        self.output_device_combo.grid(row=0, column=3, padx=(10, 20), pady=10, sticky="e")

        # Translator Service Selection (Under output device)
        service_label = ctk.CTkLabel(title_frame, text="Translator:", font=ctk.CTkFont(size=16))
        service_label.grid(row=1, column=2, padx=(20,10), pady=10, sticky="e")

        self.service_var = ctk.StringVar(value="Google Translate")
        service_list = ["Google Translate", "Ollama", "GPT"] # Added GPT
        self.service_combo = ctk.CTkComboBox(
            title_frame,
            values=service_list,
            variable=self.service_var,
            command=self.on_translation_service_change,
            font=ctk.CTkFont(size=14),
            width=200
        )
        self.service_combo.grid(row=1, column=3, padx=(10,20), pady=10, sticky="e")
        
        # Language Selection (Under translator service)
        lang_label = ctk.CTkLabel(title_frame, text="Target Language:", font=ctk.CTkFont(size=16))
        lang_label.grid(row=2, column=2, padx=(20, 10), pady=10, sticky="e")
        
        self.language_var = ctk.StringVar(value="English") # Set default to English in GUI
        language_list = list(self.translator.languages.keys())
        self.language_combo = ctk.CTkComboBox(
            title_frame,
            values=language_list,
            variable=self.language_var,
            command=self.on_language_change,
            font=ctk.CTkFont(size=14),
            width=200
        )
        self.language_combo.grid(row=2, column=3, padx=(10, 20), pady=10, sticky="e")
        
        # Mode Selection (Keep it on the left, below the main title)
        mode_label = ctk.CTkLabel(title_frame, text="Mode:", font=ctk.CTkFont(size=16))
        mode_label.grid(row=1, column=0, padx=(20, 10), pady=10, sticky="w")
        
        self.mode_var = ctk.StringVar(value="voice")
        mode_frame = ctk.CTkFrame(title_frame)
        mode_frame.grid(row=1, column=1, padx=10, pady=10, sticky="ew")
        
        self.voice_radio = ctk.CTkRadioButton(
            mode_frame,
            text="🎤 Voice Mode",
            variable=self.mode_var,
            value="voice",
            command=self.on_mode_change,
            font=ctk.CTkFont(size=14)
        )
        self.voice_radio.grid(row=0, column=0, padx=20, pady=10)
        
        self.typing_radio = ctk.CTkRadioButton(
            mode_frame,
            text="⌨️ Typing Mode",
            variable=self.mode_var,
            value="typing",
            command=self.on_mode_change,
            font=ctk.CTkFont(size=14)
        )
        self.typing_radio.grid(row=0, column=1, padx=20, pady=10)
        
        # Main Content Frame
        main_frame = ctk.CTkFrame(self.root)
        main_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        main_frame.grid_columnconfigure(0, weight=1)
        main_frame.grid_rowconfigure(1, weight=1)
        
        # Input Frame (for typing mode)
        self.input_frame = ctk.CTkFrame(main_frame)
        self.input_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))
        self.input_frame.grid_columnconfigure(0, weight=1)
        
        input_label = ctk.CTkLabel(
            self.input_frame,
            text="Type your message:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        input_label.grid(row=0, column=0, sticky="w", padx=20, pady=(20, 10))
        
        # Text input and button frame
        input_controls = ctk.CTkFrame(self.input_frame)
        input_controls.grid(row=1, column=0, sticky="ew", padx=20, pady=(0, 20))
        input_controls.grid_columnconfigure(0, weight=1)
        
        self.text_entry = ctk.CTkEntry(
            input_controls,
            placeholder_text="Enter English text to translate...",
            font=ctk.CTkFont(size=14),
            height=40
        )
        self.text_entry.grid(row=0, column=0, sticky="ew", padx=(0, 10), pady=10)
        self.text_entry.bind("<Return>", self.on_enter_pressed)
        
        self.translate_button = ctk.CTkButton(
            input_controls,
            text="⚡ Translate",
            command=self.translate_text,
            font=ctk.CTkFont(size=14),
            width=120
        )
        self.translate_button.grid(row=0, column=1, padx=10, pady=10)


        
        # Log/Output Frame
        log_frame = ctk.CTkFrame(main_frame)
        log_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        log_frame.grid_columnconfigure(0, weight=1)
        log_frame.grid_rowconfigure(1, weight=1)
        
        log_label = ctk.CTkLabel(
            log_frame,
            text="Translation Log:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_label.grid(row=0, column=0, sticky="w", padx=20, pady=(20, 10))
        
        # Text area for logs
        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(size=12),
            wrap="word"
        )
        self.log_text.grid(row=1, column=0, sticky="nsew", padx=20, pady=(0, 20))
        
        # Status Frame  
        status_frame = ctk.CTkFrame(self.root)
        status_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=(0, 20))
        status_frame.grid_columnconfigure(1, weight=1)
        
        status_label = ctk.CTkLabel(status_frame, text="Status:", font=ctk.CTkFont(size=14))
        status_label.grid(row=0, column=0, padx=20, pady=15)
        
        self.status_text = ctk.CTkLabel(
            status_frame,
            text="🎤 Voice mode active - Listening for speech...",
            font=ctk.CTkFont(size=14)
        )
        self.status_text.grid(row=0, column=1, sticky="w", padx=10, pady=15)
        
        # Initially hide input frame (voice mode default)
        self.input_frame.grid_remove()
        
    def setup_styles(self):
        """Setup custom styles and themes"""
        # Set window icon (if you have an icon file)
        try:
            self.root.iconbitmap("translator_icon.ico")
        except:
            pass  # Icon file not found, continue without it
    
    def on_mode_change(self):
        """Handle mode change - switches immediately"""
        mode = self.mode_var.get()
        if mode == "typing":
            self.input_frame.grid()
            self.translator.switch_to_typing_mode()
        else:
            self.input_frame.grid_remove()
            self.translator.switch_to_voice_mode()
        self.update_status_text()
    
    def on_output_device_change(self, selected_device):
        """Handle output device selection change"""
        self.translator.set_output_device(selected_device)
        self.update_status_text()

    def on_translation_service_change(self, selected_service):
        """Handle translation service change"""
        self.translator.set_translation_service(selected_service)
        self.update_status_text()

    def on_language_change(self, selected_language):
        """Handle language selection change"""
        self.translator.set_target_language(selected_language)
        self.update_status_text()

    def update_status_text(self):
        """Update the status bar text based on current mode, language, and service"""
        mode = self.mode_var.get()
        lang = self.language_var.get()
        service = self.service_var.get()
        output_device = self.output_device_var.get()

        # Get TTS engine info
        lang_info = self.translator.get_current_language_info()
        tts_engine = lang_info.get('tts_engine', 'edge')
        tts_display = "Edge TTS" if tts_engine == 'edge' else "Google TTS"

        self.status_text.configure(text=f"Mode: {mode.capitalize()} | Lang: {lang} | Service: {service} | TTS: {tts_display} | Output: {output_device}")

    def on_enter_pressed(self, event):
        """Handle Enter key in text entry"""
        self.translate_text()
        return "break"
    
    def translate_text(self):
        """Translate text input - optimized for immediate response"""
        text = self.text_entry.get().strip()
        if text:
            # Clear input immediately for better UX
            self.text_entry.delete(0, 'end')
            # Process in background for immediate UI response
            self.translator.process_text_input(text)


    
    def log_message(self, message):
        """Add message to log display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # Use after() to ensure thread safety with GUI updates
        self.root.after(0, lambda: self._update_log(formatted_message))
    
    def _update_log(self, message):
        """Update log text widget (must be called from main thread)"""
        self.log_text.insert("end", message)
        self.log_text.see("end")  # Scroll to bottom
    
    def on_closing(self):
        """Handle window closing"""
        self.translator.stop()
        self.root.destroy()
    
    def run(self):
        """Start the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    print("🎯 Whisper + Edge TTS Real-Time Translator with GUI (OPTIMIZED)")
    print("=" * 65)

    print("\n✅ Using RealtimeSTT for real-time speech recognition")
    print("This provides continuous real-time transcription capabilities!")

    # Display GPU status
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        print(f"🚀 GPU acceleration enabled: {gpu_name}")
        print(f"   CUDA version: {torch.version.cuda}")
    else:
        print("💻 Running on CPU (install CUDA-enabled PyTorch for GPU acceleration)")
    print()

    try:
        app = TranslatorGUI()
        app.run()
    except ImportError as e:
        print(f"\nError: Required package not installed: {e}")
        print("\nFor optimal performance, install with:")
        print("pip install -r requirements_optimized.txt")
        print("\nOr install manually:")
        print("pip install faster-whisper torch edge-tts pygame pyaudio numpy googletrans==4.0.0rc1 customtkinter requests pydub g4f")
        print("\nIf faster-whisper fails, fallback to:")
        print("pip install openai-whisper torch edge-tts pygame pyaudio numpy googletrans==4.0.0rc1 customtkinter requests pydub g4f")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        print("\nPlease ensure all dependencies are installed correctly and that your environment is properly configured.")
        print("Check PERFORMANCE_OPTIMIZATIONS.md for troubleshooting tips.")
