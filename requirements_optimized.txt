# Optimized requirements for real-time performance
# Install with: pip install -r requirements_optimized.txt

# Core dependencies
RealtimeSTT>=0.3.0  # Real-time speech-to-text library
torch>=2.0.0
torchaudio>=2.0.0

# Audio processing
pyaudio>=0.2.11
pydub>=0.25.1
numpy>=1.24.0

# Translation services
googletrans==4.0.0rc1
requests>=2.28.0

# Text-to-speech
edge-tts>=6.1.0
gtts>=2.3.0  # Google Text-to-Speech for additional language support

# GUI
customtkinter>=5.2.0

# Additional AI providers (optional)
g4f>=0.2.0

# Audio playback
pygame>=2.5.0

# Standard library enhancements
asyncio-throttle>=1.0.0  # For rate limiting if needed

# Performance monitoring (optional)
psutil>=5.9.0  # For system monitoring

# Note: RealtimeSTT provides real-time speech recognition with advanced voice activity detection
# For GPU acceleration, ensure you have CUDA-enabled PyTorch installed:
# pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu121
#
# GPU acceleration provides significantly faster transcription performance
# The app will automatically detect and use GPU if available
